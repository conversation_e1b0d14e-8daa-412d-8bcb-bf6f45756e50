#!/bin/bash

# SSL证书管理脚本
# 用于申请和管理多个域名的SSL证书
# 作者: SSL证书自动化管理
# 使用方法: ./ssl-cert-manager.sh [域名]

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
EMAIL="${SSL_EMAIL:-<EMAIL>}"
CERT_DIR="${SSL_CERT_DIR:-/root/certs}"
NGINX_SERVICE="${NGINX_SERVICE:-/etc/init.d/nginx}"

# 检测nginx服务管理方式
detect_nginx_service() {
    if command -v systemctl >/dev/null 2>&1 && systemctl is-active nginx >/dev/null 2>&1; then
        NGINX_SERVICE="systemctl"
        NGINX_CMD_STOP="systemctl stop nginx"
        NGINX_CMD_RESTART="systemctl restart nginx"
    elif [ -f "/etc/init.d/nginx" ]; then
        NGINX_SERVICE="/etc/init.d/nginx"
        NGINX_CMD_STOP="/etc/init.d/nginx stop"
        NGINX_CMD_RESTART="/etc/init.d/nginx restart"
    else
        log_error "无法找到nginx服务管理方式"
        exit 1
    fi
}

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查系统依赖
check_dependencies() {
    local missing_deps=()

    for cmd in curl openssl; do
        if ! command -v "$cmd" >/dev/null 2>&1; then
            missing_deps+=("$cmd")
        fi
    done

    if [ ${#missing_deps[@]} -ne 0 ]; then
        log_error "缺少必需的依赖: ${missing_deps[*]}"
        log_info "请安装缺少的依赖后重试"
        exit 1
    fi
}

# 检查网络连接
check_network() {
    log_info "检查网络连接..."
    if ! curl -s --connect-timeout 10 https://get.acme.sh/ >/dev/null; then
        log_error "无法连接到acme.sh服务器，请检查网络连接"
        exit 1
    fi
}

# 检查是否为root用户
check_root() {
    if [ "$EUID" -ne 0 ]; then
        log_error "请使用root用户运行此脚本"
        exit 1
    fi
}

# 检查域名格式
validate_domain() {
    local domain=$1
    if [[ ! $domain =~ ^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$ ]]; then
        log_error "域名格式不正确: $domain"
        exit 1
    fi
}

# 初始化acme.sh
init_acme() {
    log_info "检查acme.sh安装状态..."

    if [ ! -d ~/.acme.sh ]; then
        log_info "安装acme.sh..."
        if ! curl -s https://get.acme.sh/ | sh; then
            log_error "acme.sh安装失败"
            exit 1
        fi

        log_info "注册acme.sh账户..."
        if ! ~/.acme.sh/acme.sh --register-account -m "$EMAIL"; then
            log_error "acme.sh账户注册失败"
            exit 1
        fi

        log_success "acme.sh安装完成"
    else
        log_info "acme.sh已安装"
    fi
}

# 停止nginx服务
stop_nginx() {
    log_info "停止nginx服务..."
    if eval "$NGINX_CMD_STOP"; then
        log_success "nginx服务已停止"
    else
        log_warning "nginx服务停止失败或已经停止"
    fi
}

# 重启nginx服务
restart_nginx() {
    log_info "重启nginx服务..."
    if eval "$NGINX_CMD_RESTART"; then
        log_success "nginx服务已重启"
    else
        log_error "nginx服务重启失败"
        exit 1
    fi
}

# 创建证书目录
create_cert_dir() {
    if [ ! -d "$CERT_DIR" ]; then
        log_info "创建证书目录: $CERT_DIR"
        if ! mkdir -p "$CERT_DIR"; then
            log_error "无法创建证书目录: $CERT_DIR"
            exit 1
        fi
        # 设置适当的权限
        chmod 700 "$CERT_DIR"
        log_success "证书目录创建完成"
    fi
}

# 申请SSL证书
issue_certificate() {
    local domain=$1
    
    log_info "为域名 $domain 申请SSL证书..."
    
    if ~/.acme.sh/acme.sh --issue -d $domain --standalone --force; then
        log_success "证书申请成功: $domain"
    else
        log_error "证书申请失败: $domain"
        restart_nginx  # 确保nginx重新启动
        exit 1
    fi
}

# 安装SSL证书
install_certificate() {
    local domain=$1

    log_info "安装SSL证书: $domain"

    if ~/.acme.sh/acme.sh --installcert -d "$domain" \
        --key-file "$CERT_DIR/${domain}-key.pem" \
        --fullchain-file "$CERT_DIR/${domain}-cert.pem" \
        --reloadcmd "$NGINX_CMD_RESTART"; then
        log_success "证书安装成功: $domain"
        # 设置证书文件权限
        chmod 600 "$CERT_DIR/${domain}-key.pem" 2>/dev/null || true
        chmod 644 "$CERT_DIR/${domain}-cert.pem" 2>/dev/null || true
    else
        log_error "证书安装失败: $domain"
        exit 1
    fi
}

# 设置自动续签
setup_auto_renewal() {
    log_info "设置自动续签..."
    
    # 检查是否已经安装了cron任务
    if crontab -l 2>/dev/null | grep -q "acme.sh --cron"; then
        log_info "自动续签已设置"
    else
        ~/.acme.sh/acme.sh --install-cronjob
        log_success "自动续签设置完成"
    fi
}

# 显示证书信息
show_certificate_info() {
    local domain=$1
    
    log_info "证书文件位置:"
    echo "  私钥文件: $CERT_DIR/${domain}-key.pem"
    echo "  证书文件: $CERT_DIR/${domain}-cert.pem"
    
    if [ -f "$CERT_DIR/${domain}-cert.pem" ]; then
        log_info "证书有效期:"
        openssl x509 -in "$CERT_DIR/${domain}-cert.pem" -noout -dates
    fi
}

# 列出所有证书
list_certificates() {
    log_info "已申请的SSL证书列表:"
    ~/.acme.sh/acme.sh --list
}

# 测试证书续签
test_renewal() {
    local domain=$1
    
    log_info "测试证书续签: $domain"
    if ~/.acme.sh/acme.sh --renew -d $domain --dry-run; then
        log_success "续签测试成功: $domain"
    else
        log_error "续签测试失败: $domain"
    fi
}

# 显示帮助信息
show_help() {
    echo "SSL证书管理脚本"
    echo ""
    echo "用法:"
    echo "  $0 <域名>                    - 为指定域名申请SSL证书"
    echo "  $0 --list                   - 列出所有已申请的证书"
    echo "  $0 --test <域名>            - 测试指定域名的证书续签"
    echo "  $0 --help                   - 显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 carpool.maiziticket.com"
    echo "  $0 api.maiziticket.com"
    echo "  $0 --list"
    echo "  $0 --test carpool.maiziticket.com"
    echo ""
    echo "注意:"
    echo "  - 请确保域名已正确解析到当前服务器"
    echo "  - 脚本需要root权限运行"
    echo "  - 申请证书时会临时停止nginx服务"
}

# 主函数
main() {
    check_root
    
    case "${1:-}" in
        --help|-h)
            show_help
            exit 0
            ;;
        --list|-l)
            list_certificates
            exit 0
            ;;
        --test|-t)
            if [ -z "$2" ]; then
                log_error "请指定要测试的域名"
                show_help
                exit 1
            fi
            validate_domain "$2"
            test_renewal "$2"
            exit 0
            ;;
        "")
            log_error "请指定域名或操作"
            show_help
            exit 1
            ;;
        *)
            # 申请证书的主流程
            DOMAIN="$1"
            validate_domain "$DOMAIN"
            
            log_info "开始为域名 $DOMAIN 申请SSL证书..."
            
            # 执行申请流程
            init_acme
            create_cert_dir
            stop_nginx
            issue_certificate "$DOMAIN"
            install_certificate "$DOMAIN"
            setup_auto_renewal
            restart_nginx
            
            # 显示结果
            log_success "SSL证书申请完成!"
            show_certificate_info "$DOMAIN"
            
            log_info "提示: 证书将在到期前30天自动续签"
            ;;
    esac
}

# 脚本入口
main "$@"
